import React from "react";
import { Routes, Route, useLocation } from "react-router-dom";
import { isMobile } from "react-device-detect";
import SiteConnector from "./SiteConnector";
import { getAllUrlParams } from "../-Helpers-/UrlParameters";
import MekomotSherutSearchOnly from "../../Pages/MekomotSherut/MekomotSherutSearchOnly";
import { useAuth } from "../../context/AuthContext";
import AuthGuard from "../auth/AuthGuard";
import LoadingScreen from "../auth/LoadingScreen";

const GetPage = () => {
  const location = useLocation();
  const urlParams = getAllUrlParams(window.location.search);
  const { user, loading } = useAuth();

  console.log("GetPage rendered. Current path:", location.pathname);
  console.log("Environment variables:", {
    environment: process.env.REACT_APP_ENVIRONMENT,
    apiBaseUrl: process.env.REACT_APP_API_BASE_URL,
    apiBaseUrlDev: process.env.REACT_APP_API_BASE_URL_DEV
  });


  
  // מציג מסך טעינה בזמן בדיקת האותנטיקציה
  if (loading) {
    return <LoadingScreen />;
  }

  // Handle iframe cases first
  if (urlParams?.iframe === "1") {
    console.log("Rendering iframe route");
    return (
      <Routes>
        <Route
          path="/"
          element={
            urlParams?.menuonly === "1" ? (
              <MekomotSherutSearchOnly isMobile={!!isMobile} />
            ) : (
              <SiteConnector
                search={location.search}
                page="mekomotSherutPages"
                isMobile={!!isMobile}
              />
            )
          }
        />
        <Route
          path="/sherutPlaces"
          element={
            urlParams?.menuonly === "1" ? (
              <MekomotSherutSearchOnly isMobile={!!isMobile} />
            ) : (
              <SiteConnector
                search={location.search}
                page="mekomotSherutPages"
                isMobile={!!isMobile}
              />
            )
          }
        />
      </Routes>
    );
  }

  // נתיבים למשתמשים מחוברים
  if (user) {
    console.log("Rendering logged-in routes, pathname:", location.pathname);
    return (
      <Routes>
        <Route
          path="/sherutPlaces/*"
          element={
            <SiteConnector
              search={location.search}
              page="mekomotSherutPages"
              isMobile={!!isMobile}
            />
          }
        />
        <Route
          path="/userConsole/*"
          element={
            <AuthGuard>
              <SiteConnector
                page="userConsolePages"
                isMobile={!!isMobile}
              />
            </AuthGuard>
          }
        />
        <Route
          path="/logout"
          element={
            <SiteConnector
              page="logout"
              isMobile={!!isMobile}
            />
          }
        />
        <Route
          path="/*"
          element={
            <AuthGuard>
              <SiteConnector
                page="userConsolePages"
                isMobile={!!isMobile}
              />
            </AuthGuard>
          }
        />
      </Routes>
    );
  } else {
    console.log("Rendering non-logged in routes. Register path should work.");
    return (
      <Routes>
        <Route
          path="/register/*"
          element={
            <SiteConnector
              page="register"
              isMobile={!!isMobile}
            />
          }
        />
        <Route
          path="/login/*"
          element={
            <SiteConnector
              page="login"
              isMobile={!!isMobile}
            />
          }
        />
        <Route
          path="/reSendPassNew/*"
          element={
            <SiteConnector
              page="reSendPassNew"
              isMobile={!!isMobile}
            />
          }
        />
        <Route
          path="/loginQueryUrl/*"
          element={
            <SiteConnector
              page="loginQueryUrl"
              isMobile={!!isMobile}
            />
          }
        />
        <Route
          path="/sherutPlaces/*"
          element={
            <SiteConnector
              search={location.search}
              page="mekomotSherutPages"
              isMobile={!!isMobile}
            />
          }
        />
        <Route
          path="/*"
          element={
            <SiteConnector
              page="login"
              isMobile={!!isMobile}
            />
          }
        />
      </Routes>
    );
  }
};

export default GetPage;
