import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

const AuthContext = createContext();

// Helper to safely parse user data from localStorage
export const getSafeUserData = () => {
  try {
    const userDataStr = localStorage.getItem("userData");
    if (!userDataStr || userDataStr === "undefined" || userDataStr === "") {
      return null;
    }
    return JSON.parse(userDataStr);
  } catch (error) {
    console.error("Error parsing userData from localStorage:", error);
    return null;
  }
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  const API_BASE_URL = process.env.REACT_APP_ENVIRONMENT === "dev"
    ? process.env.REACT_APP_API_BASE_URL_DEV
    : process.env.REACT_APP_API_BASE_URL;



  // פונקציה לבדיקת תוקף הסשן מול השרת
  const checkSessionValidity = async (userData) => {
    if (!userData || !userData.SessionKey) {
      return false;
    }

    try {
      const response = await axios.post(
        `${API_BASE_URL}/api/test/checkAuthorization`,
        { sessionKey: userData.SessionKey }
      );
      return response.status === 200;
    } catch (error) {
      console.error("Session validation error:", error);
      return false;
    }
  };

  // פונקציה להתנתקות
  const logout = (message = "התנתקת מהמערכת") => {
    localStorage.removeItem("userData");
    localStorage.removeItem("rakazid");
    localStorage.removeItem("sayeretid");
    setUser(null);
    navigate("/login");
    // הוספת הודעת toast
    if (message) {
      toast.success(message);
    }
  };

  // פונקציה להתחברות
  const login = (userData) => {
    try {
      localStorage.setItem("userData", JSON.stringify(userData));
      setUser(userData);
    } catch (error) {
      console.error("Error saving user data:", error);
      setError("שגיאה בשמירת נתוני המשתמש");
    }
  };

  // בדיקת אותנטיקציה בטעינת האפליקציה
  useEffect(() => {
    const initAuth = async () => {
      setLoading(true);
      // Add timeout to prevent infinite loading
      const timeoutId = setTimeout(() => {
        console.warn("Authentication timeout reached");
        setLoading(false);
        setError("זמן טעינה חריג - נסו לרענן את הדף");
      }, 15000); // 15 second timeout

      try {
        const userData = getSafeUserData();
        console.log("AuthContext: Initializing with userData:", userData ? "exists" : "null");

        if (userData) {
          console.log("AuthContext: Checking session validity...");
          const isSessionValid = await checkSessionValidity(userData);
          console.log("AuthContext: Session valid:", isSessionValid);

          if (isSessionValid) {
            setUser(userData);
            console.log("AuthContext: User set successfully");
          } else {
            console.log("AuthContext: Session invalid, logging out");
            logout("פג תוקף החיבור שלך למערכת");
          }
        } else {
          console.log("AuthContext: No user data found");
        }
      } catch (error) {
        console.error("Authentication initialization error:", error);
        setError("שגיאה באימות המשתמש - " + error.message);
      } finally {
        clearTimeout(timeoutId);
        setLoading(false);
        console.log("AuthContext: Initialization completed");
      }
    };

    initAuth();
  }, []);

  // בדיקה תקופתית של תוקף הסשן
  useEffect(() => {
    if (!user) return;

    const sessionCheckInterval = setInterval(async () => {
      const isSessionValid = await checkSessionValidity(user);
      if (!isSessionValid) {
        logout("פג תוקף החיבור שלך למערכת");
      }
    }, 5 * 60 * 1000); // בדיקה כל 5 דקות

    return () => clearInterval(sessionCheckInterval);
  }, [user]);

  return (
    <AuthContext.Provider value={{ user, loading, error, login, logout, checkSessionValidity }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);
