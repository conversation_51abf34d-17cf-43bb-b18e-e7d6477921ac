// Environment variables fallback
if (!process.env.REACT_APP_ENVIRONMENT) {
  process.env.REACT_APP_ENVIRONMENT = 'dev';
}

if (!process.env.REACT_APP_API_BASE_URL) {
  process.env.REACT_APP_API_BASE_URL = 'https://sherut-leumi.wdev.co.il/api/';
}

if (!process.env.REACT_APP_API_BASE_URL_DEV) {
  process.env.REACT_APP_API_BASE_URL_DEV = 'https://sherut-leumi-dev.wdev.co.il/api/';
}

console.log('Environment variables loaded:', {
  REACT_APP_ENVIRONMENT: process.env.REACT_APP_ENVIRONMENT,
  REACT_APP_API_BASE_URL: process.env.REACT_APP_API_BASE_URL,
  REACT_APP_API_BASE_URL_DEV: process.env.REACT_APP_API_BASE_URL_DEV
}); 