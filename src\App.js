import React from "react";
import { <PERSON>rowserRouter as Router, Route, Routes } from "react-router-dom";
import { ToastContainer } from "react-toastify";
import { isMobile } from "react-device-detect";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import CssBaseline from "@mui/material/CssBaseline";
import { AuthProvider } from "./context/AuthContext";
import GetPage from "./Components/loginPages/getPage";

import "./App.css";
import "bootstrap/dist/css/bootstrap.min.css";
import "./css/animateNew.css";
import "./css/site.css";
import "./css/site_pages.css";
import "./css/mobile.css";
import "react-toastify/dist/ReactToastify.css";
import "./rtl-mui.css";

// Create a simple theme with RTL direction
const theme = createTheme({
  direction: 'rtl',
  typography: {
    fontFamily: [
      'fb_regular',
      'fb',
      'Assistant',
      'sans-serif',
    ].join(','),
  }
});

const App = () => (
  <ThemeProvider theme={theme}>
    <CssBaseline />
    <div dir="rtl" style={{ direction: 'rtl', textAlign: 'right' }}>
      <Router>
        <AuthProvider>
          <Routes>
            <Route path="/*" element={<GetPage />} />
          </Routes>

          <ToastContainer
            limit={1}
            position={isMobile ? "top-center" : "top-left"}
            autoClose={3000}
            hideProgressBar
            newestOnTop
            closeOnClick
            rtl
            pauseOnFocusLoss
            draggable
            pauseOnHover
            theme="colored"
          />
        </AuthProvider>
      </Router>
    </div>
  </ThemeProvider>
);

export default App;
