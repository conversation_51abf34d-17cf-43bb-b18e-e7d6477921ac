import React, { useEffect, useState } from "react";
import { BrowserRouter as Router, Route, Routes } from "react-router-dom";
import { ToastContainer } from "react-toastify";
import { isMobile } from "react-device-detect";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import CssBaseline from "@mui/material/CssBaseline";
import { AuthProvider } from "./context/AuthContext";
import GetPage from "./Components/loginPages/getPage";
import ErrorBoundary from "./Components/ErrorBoundary";

import "./App.css";
import "bootstrap/dist/css/bootstrap.min.css";
import "./css/animateNew.css";
import "./css/site.css";
import "./css/site_pages.css";
import "./css/mobile.css";
import "react-toastify/dist/ReactToastify.css";
import "./rtl-mui.css";

// Create a simple theme with RTL direction
const theme = createTheme({
  direction: 'rtl',
  typography: {
    fontFamily: [
      'fb_regular',
      'fb',
      'Assistant',
      'sans-serif',
    ].join(','),
  }
});

const App = () => {
  const [appTimeout, setAppTimeout] = useState(false);

  useEffect(() => {
    // Global timeout to prevent infinite white screen
    const globalTimeout = setTimeout(() => {
      console.error("Global app timeout reached - possible infinite loop or hang");
      setAppTimeout(true);
    }, 30000); // 30 seconds

    return () => clearTimeout(globalTimeout);
  }, []);

  if (appTimeout) {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100vh',
        padding: '20px',
        textAlign: 'center',
        backgroundColor: '#f5f5f5'
      }}>
        <h2 style={{ color: '#e74c3c', marginBottom: '20px' }}>בעיה בטעינת האפליקציה</h2>
        <p style={{ marginBottom: '20px' }}>האפליקציה לא הצליחה להיטען במהלך זמן סביר</p>
        <button
          onClick={() => window.location.reload()}
          style={{
            padding: '15px 30px',
            backgroundColor: '#3498db',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer',
            fontSize: '16px'
          }}
        >
          רענן דף
        </button>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <div dir="rtl" style={{ direction: 'rtl', textAlign: 'right' }}>
          <Router>
            <AuthProvider>
              <Routes>
                <Route path="/*" element={<GetPage />} />
              </Routes>

              <ToastContainer
                limit={1}
                position={isMobile ? "top-center" : "top-left"}
                autoClose={3000}
                hideProgressBar
                newestOnTop
                closeOnClick
                rtl
                pauseOnFocusLoss
                draggable
                pauseOnHover
                theme="colored"
              />
            </AuthProvider>
          </Router>
        </div>
      </ThemeProvider>
    </ErrorBoundary>
  );
};

export default App;
