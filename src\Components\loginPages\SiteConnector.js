  import React, { useEffect, useState } from "react";
  import { useNavigate, useLocation } from "react-router-dom";
  import Pages from "./Pages";
  import PagesRegister from "./PagesRegister";
  import PagesUserConsole from "./PagesUserConsole";
  import PagesMekomotSherut from "./PagesMekomotSherut";
  import { useAuth } from "../../context/AuthContext";
  import { toast } from "react-toastify";
  import { getAllUrlParams } from "../-Helpers-/UrlParameters";

  const SiteConnector = (props) => {
    console.log("SiteConnector rendered with page:", props.page);
    const navigate = useNavigate();
    const location = useLocation();
    const { checkSessionValidity, logout, user } = useAuth();
    const [hasError, setHasError] = useState(false);

    useEffect(() => {
      // Always check for logout parameter first, regardless of page
      const urlParams = getAllUrlParams(location.search);
      if (urlParams.logout === "1" || urlParams.logout === true) {
        console.log("Logout parameter detected, logging out...");
        logout("התנתקת מהמערכת בהצלחה");
        return;
      }

      // Handle logout page
      if (props.page === "logout") {
        console.log("Logout page detected, logging out...");
        logout("התנתקת מהמערכת בהצלחה");
        return;
      }

      // Skip session validation for mekomotSherutPages (sherutPlaces)
      if (props.page === "mekomotSherutPages") {
        console.log("Skipping session check for sherutPlaces");
        return;
      }

      const isIframe = location.pathname === '/sherutPlaces' && location.search.includes('iframe=1');
      if (!isIframe && user) {
        console.log("Checking session...");
        const validateSession = async () => {
          const isValid = await checkSessionValidity(user);
          if (!isValid) {
            toast.error("פג תוקף החיבור שלך למערכת, אנא התחבר/י מחדש");
            logout("פג תוקף החיבור שלך למערכת");
          }
        };
        validateSession();
      }
    }, [location, user, checkSessionValidity, logout, props.page]);

    // Error fallback
    if (hasError) {
      return (
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100vh',
          padding: '20px',
          textAlign: 'center'
        }}>
          <h3 style={{ color: '#e74c3c', marginBottom: '15px' }}>שגיאה בטעינת הדף</h3>
          <p style={{ marginBottom: '15px' }}>אירעה שגיאה בטעינת הדף</p>
          <button
            onClick={() => window.location.reload()}
            style={{
              padding: '10px 20px',
              backgroundColor: '#3498db',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            רענן דף
          </button>
        </div>
      );
    }

    try {
      if (
        props.page === "register" ||
        props.page === "login" ||
        props.page === "reSendPass" ||
        props.page === "loginQueryUrl"
      ) {
        return <PagesRegister {...props} />;
      } else if (props.page === "userConsolePages") {
        console.log("Rendering PagesUserConsole for userConsolePages");
        return <PagesUserConsole {...props} />;
      } else if (props.page === "mekomotSherutPages") {
        return <PagesMekomotSherut {...props} />;
      } else {
        return <Pages {...props} />;
      }
    } catch (error) {
      console.error("SiteConnector render error:", error);
      setHasError(true);
      return null;
    }
  };

  export default SiteConnector;
