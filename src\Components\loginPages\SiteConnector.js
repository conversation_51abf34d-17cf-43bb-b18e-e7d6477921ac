  import React, { useEffect } from "react";
  import { useNavigate, useLocation } from "react-router-dom";
  import Pages from "./Pages";
  import PagesRegister from "./PagesRegister";
  import PagesUserConsole from "./PagesUserConsole";
  import PagesMekomotSherut from "./PagesMekomotSherut";
  import { useAuth } from "../../context/AuthContext";
  import { toast } from "react-toastify";
  import { getAllUrlParams } from "../-Helpers-/UrlParameters";

  const SiteConnector = (props) => {
    console.log("SiteConnector rendered with page:", props.page);
    const navigate = useNavigate();
    const location = useLocation();
    const { checkSessionValidity, logout, user } = useAuth();
    
    useEffect(() => {
      // Always check for logout parameter first, regardless of page
      const urlParams = getAllUrlParams(location.search);
      if (urlParams.logout === "1" || urlParams.logout === true) {
        console.log("Logout parameter detected, logging out...");
        logout("התנתקת מהמערכת בהצלחה");
        return;
      }
      
      // Handle logout page
      if (props.page === "logout") {
        console.log("Logout page detected, logging out...");
        logout("התנתקת מהמערכת בהצלחה");
        return;
      }
      
      // Skip session validation for mekomotSherutPages (sherutPlaces)
      if (props.page === "mekomotSherutPages") {
        console.log("Skipping session check for sherutPlaces");
        return;
      }
      
      const isIframe = location.pathname === '/sherutPlaces' && location.search.includes('iframe=1');
      if (!isIframe && user) {
        console.log("Checking session...");
        const validateSession = async () => {
          const isValid = await checkSessionValidity(user);
          if (!isValid) {
            toast.error("פג תוקף החיבור שלך למערכת, אנא התחבר/י מחדש");
            logout("פג תוקף החיבור שלך למערכת");
          }
        };
        validateSession();
      }
    }, [location, user, checkSessionValidity, logout, props.page]);

    if (
      props.page === "register" ||
      props.page === "login" ||
      props.page === "reSendPass" ||
      props.page === "loginQueryUrl"
    ) {
      return <PagesRegister {...props} />;
    } else if (props.page === "userConsolePages") {
      console.log("Rendering PagesUserConsole for userConsolePages");
      return <PagesUserConsole {...props} />;
    } else if (props.page === "mekomotSherutPages") {
      return <PagesMekomotSherut {...props} />;
    } else {
      return <Pages {...props} />;
    }
  };

  export default SiteConnector;
