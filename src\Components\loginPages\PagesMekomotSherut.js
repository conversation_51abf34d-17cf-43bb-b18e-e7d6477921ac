import React from 'react';
import { Routes, Route } from 'react-router-dom'; // removed BrowserRouter

import MekomotSherutIndex from '../../Pages/MekomotSherut/MekomotSherutIndex';

const PagesMekomotSherut = (props) => {
    // Add debug logging
    console.log("PagesMekomotSherut rendered with props:", props);
    
    return (
        <React.Fragment>
            <Routes>
                {/* Use relative paths for nested routes */}
                <Route path="/" element={<MekomotSherutIndex {...props} page='sherutPlacesIndex' />} />
            </Routes>
        </React.Fragment>
    );
};

export default PagesMekomotSherut;
